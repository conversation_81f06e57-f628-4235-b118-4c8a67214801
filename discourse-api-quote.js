// ==UserScript==
// @name         Discourse 智能引用显示脚本
// @namespace    http://tampermonkey.net/
// @version      3.0.0
// @description  智能获取回复内容并显示引用，类似Discuz论坛效果
// <AUTHOR>
// @match        https://linux.do/*
// @match        https://*.discourse.org/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置
    const CONFIG = {
        DEBUG: true,
        CACHE_TTL: 10 * 60 * 1000, // 10分钟缓存
        MAX_QUOTE_LENGTH: 300, // 引用内容最大长度
        PROCESS_DELAY: 50, // 处理延迟
        CLICK_DELAY: 100, // 点击延迟
        WAIT_TIMEOUT: 2000 // 等待超时
    };

    // 日志函数
    const log = CONFIG.DEBUG ? (...args) => console.log('[Discourse Quote]', ...args) : () => {};

    // 缓存系统
    const postCache = new Map();

    // 清理缓存
    function cleanCache() {
        const now = Date.now();
        for (const [key, value] of postCache.entries()) {
            if (now - value.timestamp > CONFIG.CACHE_TTL) {
                postCache.delete(key);
            }
        }
    }

    // 定期清理缓存
    setInterval(cleanCache, CONFIG.CACHE_TTL);

    // 智能获取帖子数据 - 结合点击和解析
    async function getPostDataSmart(post, replyToPostId) {
        try {
            log(`智能获取帖子数据: ${replyToPostId}`);

            // 检查缓存
            const cached = postCache.get(replyToPostId);
            if (cached && (Date.now() - cached.timestamp < CONFIG.CACHE_TTL)) {
                log(`使用缓存数据: ${replyToPostId}`);
                return cached.data;
            }

            // 首先尝试查找页面上已存在的帖子
            const existingPost = findExistingPost(replyToPostId);
            if (existingPost) {
                const postData = extractPostDataFromElement(existingPost);
                if (postData) {
                    // 缓存数据
                    postCache.set(replyToPostId, {
                        data: postData,
                        timestamp: Date.now()
                    });
                    return postData;
                }
            }

            // 如果找不到，尝试触发embedded-posts加载
            const postData = await triggerEmbeddedPostsLoad(post, replyToPostId);
            if (postData) {
                // 缓存数据
                postCache.set(replyToPostId, {
                    data: postData,
                    timestamp: Date.now()
                });
                return postData;
            }

            return null;

        } catch (error) {
            log(`智能获取帖子数据失败: ${replyToPostId}`, error);
            return null;
        }
    }

    // 查找页面上已存在的帖子
    function findExistingPost(replyToPostId) {
        // 提取帖子号
        let postNumber = null;

        if (replyToPostId.includes('embedded-posts__top--')) {
            const match = replyToPostId.match(/embedded-posts__top--(\d+)/);
            if (match && match[1]) {
                postNumber = match[1];
            }
        } else if (replyToPostId.includes('embedded-posts__')) {
            const match = replyToPostId.match(/embedded-posts__(\d+)/);
            if (match && match[1]) {
                postNumber = match[1];
            }
        } else if (replyToPostId.startsWith('top--')) {
            postNumber = replyToPostId.replace('top--', '');
        }

        if (!postNumber) return null;

        // 查找对应的帖子
        const selectors = [
            `article[id="post_${postNumber}"]`,
            `article[data-post-number="${postNumber}"]`
        ];

        for (const selector of selectors) {
            const post = document.querySelector(selector);
            if (post) {
                log(`找到已存在的帖子: ${selector}`);
                return post;
            }
        }

        return null;
    }

    // 触发embedded-posts加载
    async function triggerEmbeddedPostsLoad(post, replyToPostId) {
        try {
            log(`触发embedded-posts加载: ${replyToPostId}`);

            // 查找reply-to-tab
            const replyTab = post.querySelector('.reply-to-tab');
            if (!replyTab) {
                log('未找到reply-to-tab');
                return null;
            }

            // 模拟点击以触发加载
            log('模拟点击reply-to-tab');
            replyTab.click();

            // 等待embedded-posts出现
            const embeddedPosts = await waitForEmbeddedPosts(post, CONFIG.WAIT_TIMEOUT);
            if (!embeddedPosts) {
                log('embedded-posts加载超时');
                return null;
            }

            // 从embedded-posts中提取数据
            const postData = extractPostDataFromElement(embeddedPosts);
            if (postData) {
                log('成功从embedded-posts提取数据');
                // 隐藏embedded-posts
                embeddedPosts.style.display = 'none';
                return postData;
            }

            return null;

        } catch (error) {
            log('触发embedded-posts加载失败:', error);
            return null;
        }
    }

    // 等待embedded-posts出现
    function waitForEmbeddedPosts(post, timeout) {
        return new Promise((resolve) => {
            const startTime = Date.now();

            function checkForEmbeddedPosts() {
                const embeddedPosts = post.querySelector('.embedded-posts.top');
                if (embeddedPosts && embeddedPosts.querySelector('.cooked')) {
                    resolve(embeddedPosts);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    resolve(null);
                    return;
                }

                setTimeout(checkForEmbeddedPosts, 50);
            }

            checkForEmbeddedPosts();
        });
    }

    // 从帖子元素中提取数据
    function extractPostDataFromElement(postElement) {
        try {
            // 提取用户名
            const usernameElement = postElement.querySelector('.names .username, [data-user-card], .trigger-user-card');
            let username = '未知用户';
            if (usernameElement) {
                username = usernameElement.getAttribute('data-user-card') ||
                          usernameElement.textContent?.trim() ||
                          '未知用户';
                // 清理用户名中的特殊字符
                username = username.replace(/^[@#]+/, '').trim();
            }

            // 提取头像
            const avatarElement = postElement.querySelector('.topic-avatar img.avatar, img.avatar');
            const avatarSrc = avatarElement?.getAttribute('src') || '';

            // 提取内容
            const contentElement = postElement.querySelector('.cooked');
            if (!contentElement) {
                log('未找到帖子内容元素');
                return null;
            }

            // 克隆内容以避免修改原始DOM
            const contentClone = contentElement.cloneNode(true);

            // 清理不需要的元素
            const elementsToRemove = contentClone.querySelectorAll('script, style, .lightbox-wrapper, .onebox, .discourse-local-date');
            elementsToRemove.forEach(el => el.remove());

            // 获取纯文本用于长度检查
            const textContent = contentClone.textContent || contentClone.innerText || '';

            // 如果内容太长，截断
            let finalContent = contentClone.innerHTML;
            if (textContent.length > CONFIG.MAX_QUOTE_LENGTH) {
                finalContent = truncateText(textContent, CONFIG.MAX_QUOTE_LENGTH);
            }

            // 提取楼层号
            const postNumber = postElement.getAttribute('data-post-number') ||
                              postElement.id?.replace('post_', '') ||
                              '?';

            // 提取时间
            const timeElement = postElement.querySelector('.post-date, .crawler-post-infos time, [data-time]');
            let createdAt = new Date().toISOString();
            if (timeElement) {
                const timeStr = timeElement.getAttribute('datetime') ||
                               timeElement.getAttribute('data-time') ||
                               timeElement.textContent;
                if (timeStr) {
                    const parsedTime = new Date(timeStr);
                    if (!isNaN(parsedTime.getTime())) {
                        createdAt = parsedTime.toISOString();
                    }
                }
            }

            return {
                username: username,
                name: username,
                avatar_src: avatarSrc,
                cooked: finalContent,
                post_number: postNumber,
                created_at: createdAt
            };

        } catch (error) {
            log('提取帖子数据时出错:', error);
            return null;
        }
    }

    // 截断文本
    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    // 创建引用元素
    function createQuoteElement(postData, replyToPostId) {
        const quoteDiv = document.createElement('div');
        quoteDiv.className = 'api-quote-container';
        quoteDiv.setAttribute('data-post-id', replyToPostId);

        const avatarSrc = postData.avatar_src || '';
        const content = postData.cooked || '';
        const username = postData.username || '未知用户';
        const postNumber = postData.post_number || '?';
        const timeStr = new Date(postData.created_at).toLocaleString();

        quoteDiv.innerHTML = `
            <div class="api-quote-header">
                ${avatarSrc ? `<img src="${avatarSrc}" alt="${username}" class="api-quote-avatar">` : ''}
                <span class="api-quote-author">${username}</span>
                <span class="api-quote-floor">#${postNumber}</span>
                <span class="api-quote-time">${timeStr}</span>
            </div>
            <div class="api-quote-content">
                ${content}
            </div>
        `;

        return quoteDiv;
    }

    // 添加样式
    function addStyles() {
        if (document.getElementById('api-quote-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'api-quote-styles';
        style.textContent = `
            .api-quote-container {
                margin: 10px 0;
                padding: 12px;
                background: rgba(0, 0, 0, 0.05);
                border-left: 4px solid #007cbb;
                border-radius: 8px;
                font-size: 14px;
                position: relative;
            }
            
            .api-quote-header {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                font-weight: bold;
                color: #666;
                gap: 8px;
            }
            
            .api-quote-avatar {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                border: 1px solid #ddd;
            }
            
            .api-quote-author {
                color: #007cbb;
                font-weight: bold;
            }
            
            .api-quote-floor {
                background: #007cbb;
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 12px;
            }
            
            .api-quote-time {
                font-size: 12px;
                color: #999;
                margin-left: auto;
            }
            
            .api-quote-content {
                color: #555;
                line-height: 1.4;
                word-wrap: break-word;
            }
            
            .api-quote-content p {
                margin: 0 0 8px 0;
            }
            
            .api-quote-content p:last-child {
                margin-bottom: 0;
            }
            
            .api-quote-content img {
                max-width: 100%;
                height: auto;
                border-radius: 4px;
            }
            
            .api-quote-content code {
                background: rgba(0, 0, 0, 0.1);
                padding: 2px 4px;
                border-radius: 3px;
                font-size: 13px;
            }
        `;
        
        document.head.appendChild(style);
    }

    // 处理单个帖子
    async function processPost(post) {
        try {
            // 检查是否已经处理过
            if (post.hasAttribute('data-quote-processed')) return;

            // 查找回复标签
            const replyTab = post.querySelector('.reply-to-tab');
            if (!replyTab) return;

            const replyToPostId = replyTab.getAttribute('aria-controls');
            if (!replyToPostId) return;

            log(`处理帖子回复: ${post.id} -> ${replyToPostId}`);

            // 标记为已处理
            post.setAttribute('data-quote-processed', 'true');

            // 使用智能方法获取帖子数据
            const postData = await getPostDataSmart(post, replyToPostId);
            if (!postData) {
                log(`无法获取帖子数据: ${replyToPostId}`);
                post.removeAttribute('data-quote-processed'); // 移除标记，允许重试
                return;
            }

            // 创建引用元素
            const quoteElement = createQuoteElement(postData, replyToPostId);

            // 插入引用到帖子内容前面
            const postBody = post.querySelector('.topic-body .cooked');
            if (postBody) {
                postBody.insertBefore(quoteElement, postBody.firstChild);
                log(`成功添加引用: ${post.id} -> ${replyToPostId}`);
            }

            // 隐藏原始的回复标签
            replyTab.style.display = 'none';

        } catch (error) {
            log(`处理帖子时出错: ${post.id}`, error);
            post.removeAttribute('data-quote-processed');
        }
    }

    // 处理所有帖子
    async function processAllPosts() {
        const posts = document.querySelectorAll('article[id^="post_"]');
        log(`发现 ${posts.length} 个帖子`);

        // 逐个处理帖子，避免并发冲突
        for (let i = 0; i < posts.length; i++) {
            const post = posts[i];
            try {
                await processPost(post);
                // 添加延迟，避免过快处理
                if (i < posts.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, CONFIG.PROCESS_DELAY));
                }
            } catch (error) {
                log(`处理帖子 ${post.id} 时出错:`, error);
            }
        }
    }

    // 监听DOM变化
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            let newPosts = [];

            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) {
                            // 检查是否是新的帖子
                            if (node.matches && node.matches('article[id^="post_"]')) {
                                newPosts.push(node);
                            }
                            // 检查子元素中是否有新帖子
                            const childPosts = node.querySelectorAll && node.querySelectorAll('article[id^="post_"]');
                            if (childPosts && childPosts.length > 0) {
                                newPosts.push(...childPosts);
                            }
                        }
                    });
                }
            });

            if (newPosts.length > 0) {
                log(`检测到 ${newPosts.length} 个新帖子`);
                // 异步处理新帖子
                processNewPosts(newPosts);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        log('开始监听DOM变化');
    }

    // 处理新帖子
    async function processNewPosts(posts) {
        for (let i = 0; i < posts.length; i++) {
            const post = posts[i];
            try {
                await processPost(post);
                // 添加延迟
                if (i < posts.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, CONFIG.PROCESS_DELAY));
                }
            } catch (error) {
                log(`处理新帖子 ${post.id} 时出错:`, error);
            }
        }
    }

    // 初始化
    function init() {
        log('初始化Discourse API引用脚本');
        
        // 添加样式
        addStyles();
        
        // 处理现有帖子
        processAllPosts();
        
        // 开始监听变化
        observeChanges();
        
        log('初始化完成');
    }

    // 等待页面加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
