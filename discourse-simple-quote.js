// ==UserScript==
// @name         Discourse 简单引用脚本
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  简单有效的Discourse引用显示脚本
// <AUTHOR>
// @match        https://linux.do/*
// @match        https://*.discourse.org/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置
    const CONFIG = {
        DEBUG: true, // 设为 true 可查看详细日志
        PROCESS_DELAY: 200,
        WAIT_TIMEOUT: 3000,
        MAX_QUOTE_LENGTH: 400
    };

    // 日志函数
    const log = CONFIG.DEBUG ? (...args) => console.log('[Simple Quote]', ...args) : () => {};

    // 已处理的帖子集合
    const processedPosts = new Set();

    // 添加样式
    function addStyles() {
        if (document.getElementById('simple-quote-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'simple-quote-styles';
        style.textContent = `
            .simple-quote-container {
                margin: 10px 0 !important;
                padding: 12px !important;
                background: rgba(0, 0, 0, 0.1) !important;
                border-left: 4px solid #007cbb !important;
                border-radius: 8px !important;
                font-size: 14px !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: relative !important;
                z-index: 1000 !important;
            }
            
            .simple-quote-header {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                font-weight: bold;
                color: #666;
                gap: 8px;
            }
            
            .simple-quote-avatar {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                border: 1px solid #ddd;
            }
            
            .simple-quote-author {
                color: #007cbb;
                font-weight: bold;
            }
            
            .simple-quote-floor {
                background: #007cbb;
                color: white;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 12px;
            }
            
            .simple-quote-content {
                color: #555;
                line-height: 1.4;
                word-wrap: break-word;
            }
            
            .simple-quote-content p {
                margin: 0 0 8px 0;
            }
            
            .simple-quote-content p:last-child {
                margin-bottom: 0;
            }
        `;
        
        document.head.appendChild(style);
    }

    // 截断文本
    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    // 清理HTML内容
    function cleanContent(element) {
        if (!element) return '内容为空';

        const clone = element.cloneNode(true);

        // 移除不需要的元素
        const unwanted = clone.querySelectorAll('script, style, .lightbox-wrapper, .onebox, .cooked-selection-barrier');
        unwanted.forEach(el => el.remove());

        // 先尝试获取HTML内容
        let content = clone.innerHTML.trim();

        // 如果HTML内容为空，尝试获取文本内容
        if (!content) {
            content = clone.textContent || clone.innerText || '';
        }

        // 如果还是为空，返回提示
        if (!content) {
            return '无法获取内容';
        }

        // 截断过长的内容
        const textContent = clone.textContent || clone.innerText || '';
        if (textContent.length > CONFIG.MAX_QUOTE_LENGTH) {
            return truncateText(textContent, CONFIG.MAX_QUOTE_LENGTH);
        }

        return content;
    }

    // 提取用户名
    function extractUsername(container) {
        const selectors = [
            '.names .username',
            '[data-user-card]',
            '.trigger-user-card',
            '.username'
        ];
        
        for (const selector of selectors) {
            const element = container.querySelector(selector);
            if (element) {
                const username = element.getAttribute('data-user-card') || 
                               element.textContent?.trim();
                if (username && !username.includes('embedded-posts')) {
                    return username.replace(/^[@#]+/, '').trim();
                }
            }
        }
        
        return '未知用户';
    }

    // 创建引用元素
    function createQuoteElement(embeddedPost) {
        const username = extractUsername(embeddedPost);
        const avatar = embeddedPost.querySelector('img.avatar');
        const avatarSrc = avatar?.getAttribute('src') || '';
        const content = embeddedPost.querySelector('.cooked');
        const cleanedContent = cleanContent(content);

        log('创建引用元素:');
        log('- 用户名:', username);
        log('- 头像:', avatarSrc);
        log('- 原始内容元素:', content);
        log('- 清理后内容:', cleanedContent);
        log('- 内容长度:', cleanedContent.length);

        // 尝试获取楼层号
        const postNumber = embeddedPost.getAttribute('data-post-number') ||
                          embeddedPost.id?.replace('post_', '') || '?';

        const quoteDiv = document.createElement('div');
        quoteDiv.className = 'simple-quote-container-' + Date.now(); // 使用唯一类名

        // 使用setAttribute确保样式不被覆盖
        quoteDiv.setAttribute('style', `
            margin: 15px 0 15px 0 !important;
            padding: 15px !important;
            background: #f0f8ff !important;
            border: 2px solid #007cbb !important;
            border-radius: 10px !important;
            font-size: 14px !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 9999 !important;
            min-height: 60px !important;
            width: auto !important;
            height: auto !important;
            overflow: visible !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        `);

        // 使用更安全的方式创建内容
        const headerDiv = document.createElement('div');
        headerDiv.setAttribute('style', 'display: flex !important; align-items: center !important; margin-bottom: 10px !important; font-weight: bold !important; color: #333 !important; gap: 8px !important;');

        if (avatarSrc) {
            const avatar = document.createElement('img');
            avatar.src = avatarSrc;
            avatar.alt = username;
            avatar.setAttribute('style', 'width: 24px !important; height: 24px !important; border-radius: 4px !important; border: 1px solid #ddd !important;');
            headerDiv.appendChild(avatar);
        }

        const authorSpan = document.createElement('span');
        authorSpan.textContent = username;
        authorSpan.setAttribute('style', 'color: #007cbb !important; font-weight: bold !important; font-size: 14px !important;');
        headerDiv.appendChild(authorSpan);

        const floorSpan = document.createElement('span');
        floorSpan.textContent = '#' + postNumber;
        floorSpan.setAttribute('style', 'background: #007cbb !important; color: white !important; padding: 3px 8px !important; border-radius: 4px !important; font-size: 12px !important;');
        headerDiv.appendChild(floorSpan);

        const contentDiv = document.createElement('div');
        contentDiv.innerHTML = cleanedContent || '【内容为空】';
        contentDiv.setAttribute('style', 'color: #333 !important; line-height: 1.5 !important; word-wrap: break-word !important; font-size: 14px !important; display: block !important; visibility: visible !important; opacity: 1 !important; margin: 0 !important; padding: 0 !important;');

        const testDiv = document.createElement('div');
        testDiv.textContent = '🔵 引用区域测试标记';
        testDiv.setAttribute('style', 'color: #007cbb !important; font-size: 12px !important; margin-top: 8px !important; font-weight: bold !important;');

        quoteDiv.appendChild(headerDiv);
        quoteDiv.appendChild(contentDiv);
        quoteDiv.appendChild(testDiv);


        return quoteDiv;
    }

    // 等待embedded-posts出现
    function waitForEmbeddedPosts(post) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            function check() {
                const embeddedPosts = post.querySelector('.embedded-posts.top');
                if (embeddedPosts) {
                    const content = embeddedPosts.querySelector('.cooked');
                    if (content && content.textContent.trim()) {
                        log('找到embedded-posts内容');
                        resolve(embeddedPosts);
                        return;
                    }
                }
                
                if (Date.now() - startTime > CONFIG.WAIT_TIMEOUT) {
                    log('等待embedded-posts超时');
                    resolve(null);
                    return;
                }
                
                setTimeout(check, 100);
            }
            
            check();
        });
    }

    // 处理单个帖子
    async function processPost(post) {
        try {
            const postId = post.id;
            if (processedPosts.has(postId)) return;
            
            const replyTab = post.querySelector('.reply-to-tab');
            if (!replyTab) return;
            
            log(`处理帖子: ${postId}`);
            processedPosts.add(postId);
            
            // 点击reply-to-tab触发加载
            replyTab.click();
            
            // 等待embedded-posts出现
            const embeddedPosts = await waitForEmbeddedPosts(post);
            if (!embeddedPosts) {
                log(`未找到embedded-posts: ${postId}`);
                processedPosts.delete(postId); // 允许重试
                return;
            }
            
            // 创建引用
            const quoteElement = createQuoteElement(embeddedPosts);
            
            // 插入引用
            const postBody = post.querySelector('.topic-body .cooked');
            if (postBody) {
                postBody.insertBefore(quoteElement, postBody.firstChild);
                log(`成功添加引用: ${postId}`);

                // 强制重新应用样式，防止被覆盖
                setTimeout(() => {
                    if (quoteElement.parentNode) {
                        quoteElement.setAttribute('style', `
                            margin: 15px 0 15px 0 !important;
                            padding: 15px !important;
                            background: #f0f8ff !important;
                            border: 2px solid #007cbb !important;
                            border-radius: 10px !important;
                            font-size: 14px !important;
                            display: block !important;
                            visibility: visible !important;
                            opacity: 1 !important;
                            position: relative !important;
                            z-index: 9999 !important;
                            min-height: 60px !important;
                            width: auto !important;
                            height: auto !important;
                            overflow: visible !important;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
                        `);
                        log(`重新应用样式: ${postId}`);
                    }
                }, 100);

                // 再次检查，如果还是不可见，尝试更激进的方法
                setTimeout(() => {
                    if (quoteElement.parentNode && (quoteElement.offsetHeight === 0 || quoteElement.style.display === 'none')) {
                        log(`检测到引用被隐藏，尝试修复: ${postId}`);
                        quoteElement.style.setProperty('display', 'block', 'important');
                        quoteElement.style.setProperty('visibility', 'visible', 'important');
                        quoteElement.style.setProperty('opacity', '1', 'important');
                    }
                }, 500);

            } else {
                log(`未找到插入位置: ${postId}`);
                // 尝试其他位置
                const topicBody = post.querySelector('.topic-body');
                if (topicBody) {
                    topicBody.insertBefore(quoteElement, topicBody.firstChild);
                    log(`使用备用位置插入引用: ${postId}`);
                }
            }
            
            // 隐藏原始元素
            replyTab.style.display = 'none';
            embeddedPosts.style.display = 'none';
            
        } catch (error) {
            log(`处理帖子出错: ${post.id}`, error);
            processedPosts.delete(post.id);
        }
    }

    // 处理所有帖子
    async function processAllPosts() {
        const posts = document.querySelectorAll('article[id^="post_"]');
        log(`发现 ${posts.length} 个帖子`);
        
        for (const post of posts) {
            const replyTab = post.querySelector('.reply-to-tab');
            if (replyTab && !processedPosts.has(post.id)) {
                await processPost(post);
                await new Promise(resolve => setTimeout(resolve, CONFIG.PROCESS_DELAY));
            }
        }
    }

    // 监听新帖子
    function observeChanges() {
        const observer = new MutationObserver((mutations) => {
            let hasNewPosts = false;
            
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) {
                        if (node.matches && node.matches('article[id^="post_"]')) {
                            hasNewPosts = true;
                        }
                        const newPosts = node.querySelectorAll && node.querySelectorAll('article[id^="post_"]');
                        if (newPosts && newPosts.length > 0) {
                            hasNewPosts = true;
                        }
                    }
                });
            });
            
            if (hasNewPosts) {
                log('检测到新帖子');
                setTimeout(processAllPosts, 500);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 初始化
    function init() {
        log('初始化简单引用脚本');
        addStyles();
        processAllPosts();
        observeChanges();
    }

    // 启动
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
